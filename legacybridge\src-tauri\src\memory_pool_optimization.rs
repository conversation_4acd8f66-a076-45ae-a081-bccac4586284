// Memory Pool Implementation for LegacyBridge
// Reduces allocation overhead and improves performance

use std::sync::{Arc, Mutex};
use std::collections::VecDeque;
use std::mem;
use once_cell::sync::Lazy;
use crate::conversion::types::RtfNode;

/// Thread-safe object pool for reusable allocations
pub struct ObjectPool<T> {
    pool: Arc<Mutex<VecDeque<T>>>,
    factory: Box<dyn Fn() -> T + Send + Sync>,
    max_size: usize,
    reset_fn: Option<Arc<dyn Fn(&mut T) + Send + Sync>>,
}

impl<T: Send + 'static> ObjectPool<T> {
    /// Create a new object pool
    pub fn new<F>(max_size: usize, factory: F) -> Self
    where
        F: Fn() -> T + Send + Sync + 'static,
    {
        Self {
            pool: Arc::new(Mutex::new(VecDeque::with_capacity(max_size))),
            factory: Box::new(factory),
            max_size,
            reset_fn: None,
        }
    }
    
    /// Create a pool with a reset function
    pub fn with_reset<F, R>(max_size: usize, factory: F, reset: R) -> Self
    where
        F: Fn() -> T + Send + Sync + 'static,
        R: Fn(&mut T) + Send + Sync + 'static,
    {
        Self {
            pool: Arc::new(Mutex::new(VecDeque::with_capacity(max_size))),
            factory: Box::new(factory),
            max_size,
            reset_fn: Some(Arc::new(reset)),
        }
    }
    
    /// Acquire an object from the pool
    pub fn acquire(&self) -> PooledObject<T> {
        let mut pool = self.pool.lock()
            .expect("Failed to acquire pool lock");
        let obj = pool.pop_front().unwrap_or_else(|| (self.factory)());
        
        PooledObject {
            value: Some(obj),
            pool: Arc::clone(&self.pool),
            reset_fn: self.reset_fn.as_ref().map(|f| Arc::clone(f)),
        }
    }
    
    /// Get current pool size
    pub fn size(&self) -> usize {
        self.pool.lock()
            .expect("Failed to acquire pool lock")
            .len()
    }
}

/// RAII wrapper for pooled objects
pub struct PooledObject<T> {
    value: Option<T>,
    pool: Arc<Mutex<VecDeque<T>>>,
    reset_fn: Option<Arc<dyn Fn(&mut T) + Send + Sync>>,
}

impl<T> PooledObject<T> {
    /// Get a reference to the pooled value
    pub fn as_ref(&self) -> &T {
        self.value.as_ref()
            .expect("PooledObject value should always be Some until dropped")
    }
    
    /// Get a mutable reference to the pooled value
    pub fn as_mut(&mut self) -> &mut T {
        self.value.as_mut()
            .expect("PooledObject value should always be Some until dropped")
    }
}

impl<T> Drop for PooledObject<T> {
    fn drop(&mut self) {
        if let Some(mut value) = self.value.take() {
            // Reset the object if a reset function is provided
            if let Some(reset_fn) = &self.reset_fn {
                (reset_fn)(&mut value);
            }
            
            // Return to pool if there's space
            let mut pool = self.pool.lock()
                .expect("Failed to acquire pool lock");
            if pool.len() < pool.capacity() {
                pool.push_back(value);
            }
        }
    }
}

impl<T> std::ops::Deref for PooledObject<T> {
    type Target = T;
    
    fn deref(&self) -> &Self::Target {
        self.as_ref()
    }
}

impl<T> std::ops::DerefMut for PooledObject<T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        self.as_mut()
    }
}

/// Optimized global memory pools for common types
/// Based on SIMD performance analysis and usage patterns
pub static STRING_POOL: Lazy<ObjectPool<String>> = Lazy::new(|| {
    ObjectPool::with_reset(
        256,  // Increased pool size for better reuse
        || String::with_capacity(512),  // Larger initial capacity for SIMD operations
        |s| s.clear(),
    )
});

pub static SMALL_STRING_POOL: Lazy<ObjectPool<String>> = Lazy::new(|| {
    ObjectPool::with_reset(
        512,  // More small strings for control words and tokens
        || String::with_capacity(64),   // Optimized for RTF control words
        |s| s.clear(),
    )
});

pub static VEC_U8_POOL: Lazy<ObjectPool<Vec<u8>>> = Lazy::new(|| {
    ObjectPool::with_reset(
        128,  // Increased for better buffer reuse
        || Vec::with_capacity(8192),  // Larger buffers for SIMD processing
        |v| v.clear(),
    )
});

pub static SIMD_BUFFER_POOL: Lazy<ObjectPool<Vec<u8>>> = Lazy::new(|| {
    ObjectPool::with_reset(
        64,   // Specialized pool for SIMD operations
        || Vec::with_capacity(32768), // 32KB aligned for AVX2 operations
        |v| v.clear(),
    )
});

pub static NODE_VEC_POOL: Lazy<ObjectPool<Vec<RtfNode>>> = Lazy::new(|| {
    ObjectPool::with_reset(
        64,   // Increased for document tree building
        || Vec::with_capacity(200),  // Larger capacity for complex documents
        |v| v.clear(),
    )
});

/// Performance metrics for memory pool optimization
#[derive(Debug, Clone)]
pub struct PoolMetrics {
    pub total_allocations: usize,
    pub total_deallocations: usize,
    pub peak_pool_size: usize,
    pub current_pool_size: usize,
    pub cache_hits: usize,
    pub cache_misses: usize,
    pub memory_saved_bytes: usize,
}

impl PoolMetrics {
    pub fn new() -> Self {
        Self {
            total_allocations: 0,
            total_deallocations: 0,
            peak_pool_size: 0,
            current_pool_size: 0,
            cache_hits: 0,
            cache_misses: 0,
            memory_saved_bytes: 0,
        }
    }

    pub fn hit_rate(&self) -> f64 {
        if self.total_allocations == 0 {
            0.0
        } else {
            self.cache_hits as f64 / self.total_allocations as f64
        }
    }
}

/// Arena allocator for temporary allocations with SIMD optimization
pub struct Arena {
    chunks: Vec<Vec<u8>>,
    current_chunk: usize,
    current_offset: usize,
    chunk_size: usize,
    metrics: PoolMetrics,
}

impl Arena {
    pub fn new(chunk_size: usize) -> Self {
        let mut chunks = Vec::new();
        // Align chunk size to 32 bytes for SIMD operations
        let aligned_chunk_size = (chunk_size + 31) & !31;
        chunks.push(vec![0; aligned_chunk_size]);

        Self {
            chunks,
            current_chunk: 0,
            current_offset: 0,
            chunk_size: aligned_chunk_size,
            metrics: PoolMetrics::new(),
        }
    }

    /// Get performance metrics for this arena
    pub fn metrics(&self) -> &PoolMetrics {
        &self.metrics
    }
    
    /// Allocate bytes from the arena with SIMD alignment
    pub fn alloc(&mut self, size: usize) -> &mut [u8] {
        // Align to 32 bytes for SIMD operations
        let align = 32;
        let aligned_size = (size + align - 1) & !(align - 1);

        self.metrics.total_allocations += 1;

        // Check if we need a new chunk
        if self.current_offset + aligned_size > self.chunk_size {
            let new_chunk_size = self.chunk_size.max(aligned_size);
            self.chunks.push(vec![0; new_chunk_size]);
            self.current_chunk += 1;
            self.current_offset = 0;
            self.metrics.cache_misses += 1;
        } else {
            self.metrics.cache_hits += 1;
        }

        let chunk = &mut self.chunks[self.current_chunk];
        let start = self.current_offset;
        self.current_offset += aligned_size;

        // Update metrics
        self.metrics.memory_saved_bytes += aligned_size - size; // Track alignment overhead

        &mut chunk[start..start + size]
    }
    
    /// Allocate a string in the arena
    pub fn alloc_str(&mut self, s: &str) -> &str {
        let bytes = self.alloc(s.len());
        bytes.copy_from_slice(s.as_bytes());
        unsafe { std::str::from_utf8_unchecked(bytes) }
    }
    
    /// Reset the arena for reuse
    pub fn reset(&mut self) {
        self.current_chunk = 0;
        self.current_offset = 0;

        // Update metrics
        self.metrics.total_deallocations += 1;
        self.metrics.current_pool_size = 1;
        if self.chunks.len() > self.metrics.peak_pool_size {
            self.metrics.peak_pool_size = self.chunks.len();
        }

        // Keep only the first chunk to avoid reallocation
        self.chunks.truncate(1);
    }
}

/// Global memory pool manager for optimization
pub struct MemoryPoolManager {
    pub string_pool_metrics: Arc<Mutex<PoolMetrics>>,
    pub buffer_pool_metrics: Arc<Mutex<PoolMetrics>>,
    pub node_pool_metrics: Arc<Mutex<PoolMetrics>>,
}

impl MemoryPoolManager {
    pub fn new() -> Self {
        Self {
            string_pool_metrics: Arc::new(Mutex::new(PoolMetrics::new())),
            buffer_pool_metrics: Arc::new(Mutex::new(PoolMetrics::new())),
            node_pool_metrics: Arc::new(Mutex::new(PoolMetrics::new())),
        }
    }

    /// Get comprehensive memory pool statistics
    pub fn get_stats(&self) -> MemoryPoolStats {
        let string_metrics = self.string_pool_metrics.lock().unwrap().clone();
        let buffer_metrics = self.buffer_pool_metrics.lock().unwrap().clone();
        let node_metrics = self.node_pool_metrics.lock().unwrap().clone();

        MemoryPoolStats {
            string_pool: string_metrics,
            buffer_pool: buffer_metrics,
            node_pool: node_metrics,
            total_memory_saved: 0, // Will be calculated
        }
    }

    /// Optimize pool sizes based on usage patterns
    pub fn optimize_pools(&self) -> PoolOptimizationReport {
        let stats = self.get_stats();

        PoolOptimizationReport {
            recommended_string_pool_size: calculate_optimal_pool_size(&stats.string_pool),
            recommended_buffer_pool_size: calculate_optimal_pool_size(&stats.buffer_pool),
            recommended_node_pool_size: calculate_optimal_pool_size(&stats.node_pool),
            efficiency_score: calculate_efficiency_score(&stats),
        }
    }
}

#[derive(Debug, Clone)]
pub struct MemoryPoolStats {
    pub string_pool: PoolMetrics,
    pub buffer_pool: PoolMetrics,
    pub node_pool: PoolMetrics,
    pub total_memory_saved: usize,
}

#[derive(Debug)]
pub struct PoolOptimizationReport {
    pub recommended_string_pool_size: usize,
    pub recommended_buffer_pool_size: usize,
    pub recommended_node_pool_size: usize,
    pub efficiency_score: f64,
}

fn calculate_optimal_pool_size(metrics: &PoolMetrics) -> usize {
    // Calculate optimal pool size based on hit rate and peak usage
    let base_size = metrics.peak_pool_size;
    let hit_rate = metrics.hit_rate();

    if hit_rate > 0.9 {
        base_size // High hit rate, keep current size
    } else if hit_rate > 0.7 {
        (base_size as f64 * 1.5) as usize // Moderate hit rate, increase size
    } else {
        (base_size as f64 * 2.0) as usize // Low hit rate, double size
    }
}

fn calculate_efficiency_score(stats: &MemoryPoolStats) -> f64 {
    let avg_hit_rate = (stats.string_pool.hit_rate() +
                       stats.buffer_pool.hit_rate() +
                       stats.node_pool.hit_rate()) / 3.0;
    avg_hit_rate * 100.0
}

/// Zero-copy string builder using memory pool
pub struct PooledStringBuilder {
    buffer: PooledObject<String>,
}

impl PooledStringBuilder {
    pub fn new() -> Self {
        Self {
            buffer: STRING_POOL.acquire(),
        }
    }
    
    pub fn with_capacity(capacity: usize) -> Self {
        let mut builder = Self::new();
        builder.buffer.reserve(capacity);
        builder
    }
    
    pub fn push_str(&mut self, s: &str) {
        self.buffer.push_str(s);
    }
    
    pub fn push(&mut self, ch: char) {
        self.buffer.push(ch);
    }
    
    pub fn len(&self) -> usize {
        self.buffer.len()
    }
    
    pub fn capacity(&self) -> usize {
        self.buffer.capacity()
    }
    
    pub fn finish(self) -> String {
        let mut buffer = self.buffer;
        mem::take(&mut *buffer)
    }
}

/// Optimized converter using memory pools
pub struct PooledRtfConverter {
    string_pool: ObjectPool<String>,
    vec_pool: ObjectPool<Vec<u8>>,
    arena: Arena,
}

impl PooledRtfConverter {
    pub fn new() -> Self {
        Self {
            string_pool: ObjectPool::with_reset(16, || String::with_capacity(256), |s| s.clear()),
            vec_pool: ObjectPool::with_reset(16, || Vec::with_capacity(1024), |v| v.clear()),
            arena: Arena::new(64 * 1024), // 64KB chunks
        }
    }
    
    pub fn convert(&mut self, input: &str) -> Result<String, Box<dyn std::error::Error>> {
        // Use pooled resources
        let mut output = self.string_pool.acquire();
        let mut temp_buffer = self.vec_pool.acquire();
        
        // Process conversion using pooled resources
        self.process_with_pools(input, &mut output, &mut temp_buffer)?;
        
        // Extract result
        Ok(mem::take(&mut *output))
    }
    
    fn process_with_pools(
        &mut self,
        input: &str,
        output: &mut String,
        temp: &mut Vec<u8>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Use arena for temporary string allocations
        let temp_str = self.arena.alloc_str("temporary");
        
        // Process input...
        output.push_str("Processed: ");
        output.push_str(input);
        
        Ok(())
    }
    
    pub fn reset(&mut self) {
        self.arena.reset();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_object_pool() {
        let pool: ObjectPool<Vec<u8>> = ObjectPool::with_reset(
            2,
            || vec![0; 100],
            |v| v.clear(),
        );
        
        {
            let mut obj1 = pool.acquire();
            obj1.push(1);
            assert_eq!(obj1.len(), 1);
        }
        
        assert_eq!(pool.size(), 1);
        
        {
            let obj2 = pool.acquire();
            assert_eq!(obj2.len(), 0); // Should be reset
        }
    }
    
    #[test]
    fn test_arena_allocator() {
        let mut arena = Arena::new(1024);
        
        let s1 = arena.alloc_str("hello");
        assert_eq!(s1, "hello");

        let s2 = arena.alloc_str("world");
        assert_eq!(s2, "world");
        
        arena.reset();
        
        let s3 = arena.alloc_str("reused");
        assert_eq!(s3, "reused");
    }
    
    #[test]
    fn test_pooled_string_builder() {
        let mut builder = PooledStringBuilder::new();
        builder.push_str("Hello, ");
        builder.push_str("World!");
        
        let result = builder.finish();
        assert_eq!(result, "Hello, World!");
    }
}