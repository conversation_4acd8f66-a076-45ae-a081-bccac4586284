// Enhanced Batch Processing System
// Provides comprehensive bulk document conversion with advanced progress tracking,
// error handling, retry mechanisms, and performance optimization

use std::sync::{Arc, atomic::{AtomicUsize, AtomicU64, Ordering}};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, mpsc, Semaphore};
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use crate::conversion::types::{ConversionResult, ConversionError};
use crate::pipeline::concurrent_processor_v2::{ConcurrentProcessorV2, ConversionRequest, ConversionResponse};

/// Enhanced batch processing configuration
#[derive(Debug, Clone)]
pub struct BatchProcessorConfig {
    /// Maximum number of concurrent conversions
    pub max_concurrent: usize,
    /// Maximum batch size
    pub max_batch_size: usize,
    /// Timeout per document
    pub document_timeout: Duration,
    /// Overall batch timeout
    pub batch_timeout: Duration,
    /// Retry attempts for failed documents
    pub max_retries: u32,
    /// Retry delay
    pub retry_delay: Duration,
    /// Progress update interval
    pub progress_interval: Duration,
    /// Enable detailed metrics
    pub enable_metrics: bool,
}

impl Default for BatchProcessorConfig {
    fn default() -> Self {
        Self {
            max_concurrent: 10,
            max_batch_size: 1000,
            document_timeout: Duration::from_secs(30),
            batch_timeout: Duration::from_secs(3600), // 1 hour
            max_retries: 3,
            retry_delay: Duration::from_millis(500),
            progress_interval: Duration::from_millis(100),
            enable_metrics: true,
        }
    }
}

/// Batch job status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BatchStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
    Paused,
}

/// Individual document status within a batch
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentStatus {
    pub id: String,
    pub filename: String,
    pub status: DocumentProcessingStatus,
    pub attempts: u32,
    pub processing_time_ms: u64,
    pub error_message: Option<String>,
    pub size_bytes: usize,
    pub result_size_bytes: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DocumentProcessingStatus {
    Pending,
    Processing,
    Completed,
    Failed,
    Retrying,
    Skipped,
}

/// Comprehensive batch progress information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchProgress {
    pub batch_id: String,
    pub status: BatchStatus,
    pub total_documents: usize,
    pub completed_documents: usize,
    pub failed_documents: usize,
    pub skipped_documents: usize,
    pub progress_percentage: f64,
    pub estimated_time_remaining_ms: Option<u64>,
    pub elapsed_time_ms: u64,
    pub throughput_docs_per_second: f64,
    pub current_document: Option<String>,
    pub documents: Vec<DocumentStatus>,
    pub metrics: BatchMetrics,
}

/// Detailed batch processing metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchMetrics {
    pub total_input_bytes: u64,
    pub total_output_bytes: u64,
    pub average_processing_time_ms: f64,
    pub peak_memory_usage_mb: f64,
    pub cpu_utilization_percent: f64,
    pub error_rate_percent: f64,
    pub retry_rate_percent: f64,
    pub compression_ratio: f64,
}

impl Default for BatchMetrics {
    fn default() -> Self {
        Self {
            total_input_bytes: 0,
            total_output_bytes: 0,
            average_processing_time_ms: 0.0,
            peak_memory_usage_mb: 0.0,
            cpu_utilization_percent: 0.0,
            error_rate_percent: 0.0,
            retry_rate_percent: 0.0,
            compression_ratio: 1.0,
        }
    }
}

/// Batch processing request
#[derive(Debug, Clone)]
pub struct BatchRequest {
    pub batch_id: String,
    pub documents: Vec<ConversionRequest>,
    pub config: BatchProcessorConfig,
    pub priority: BatchPriority,
    pub callback_url: Option<String>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum BatchPriority {
    Low,
    Normal,
    High,
    Critical,
}

/// Enhanced batch processor
pub struct EnhancedBatchProcessor {
    config: BatchProcessorConfig,
    processor: Arc<ConcurrentProcessorV2>,
    active_batches: Arc<RwLock<HashMap<String, BatchProgress>>>,
    progress_senders: Arc<RwLock<HashMap<String, mpsc::UnboundedSender<BatchProgress>>>>,
    metrics: Arc<BatchProcessorMetrics>,
}

#[derive(Debug)]
struct BatchProcessorMetrics {
    total_batches_processed: AtomicUsize,
    total_documents_processed: AtomicU64,
    total_processing_time_ms: AtomicU64,
    total_errors: AtomicUsize,
    total_retries: AtomicUsize,
}

impl EnhancedBatchProcessor {
    /// Create new enhanced batch processor
    pub fn new(config: BatchProcessorConfig) -> Self {
        Self {
            processor: Arc::new(ConcurrentProcessorV2::new()),
            active_batches: Arc::new(RwLock::new(HashMap::new())),
            progress_senders: Arc::new(RwLock::new(HashMap::new())),
            metrics: Arc::new(BatchProcessorMetrics {
                total_batches_processed: AtomicUsize::new(0),
                total_documents_processed: AtomicU64::new(0),
                total_processing_time_ms: AtomicU64::new(0),
                total_errors: AtomicUsize::new(0),
                total_retries: AtomicUsize::new(0),
            }),
            config,
        }
    }

    /// Submit a batch for processing
    pub async fn submit_batch(
        &self,
        request: BatchRequest,
    ) -> Result<mpsc::UnboundedReceiver<BatchProgress>, ConversionError> {
        // Validate batch request
        self.validate_batch_request(&request)?;

        let (progress_tx, progress_rx) = mpsc::unbounded_channel();
        
        // Initialize batch progress
        let mut progress = BatchProgress {
            batch_id: request.batch_id.clone(),
            status: BatchStatus::Pending,
            total_documents: request.documents.len(),
            completed_documents: 0,
            failed_documents: 0,
            skipped_documents: 0,
            progress_percentage: 0.0,
            estimated_time_remaining_ms: None,
            elapsed_time_ms: 0,
            throughput_docs_per_second: 0.0,
            current_document: None,
            documents: request.documents.iter().map(|doc| DocumentStatus {
                id: doc.id.clone(),
                filename: doc.id.clone(), // Use ID as filename for now
                status: DocumentProcessingStatus::Pending,
                attempts: 0,
                processing_time_ms: 0,
                error_message: None,
                size_bytes: doc.content.as_bytes().map(|b| b.len()).unwrap_or(0),
                result_size_bytes: None,
            }).collect(),
            metrics: BatchMetrics::default(),
        };

        // Store batch progress and sender
        self.active_batches.write().await.insert(request.batch_id.clone(), progress.clone());
        self.progress_senders.write().await.insert(request.batch_id.clone(), progress_tx);

        // Start batch processing
        let processor = self.clone();
        tokio::spawn(async move {
            processor.process_batch_internal(request).await;
        });

        Ok(progress_rx)
    }

    /// Get current batch progress
    pub async fn get_batch_progress(&self, batch_id: &str) -> Option<BatchProgress> {
        self.active_batches.read().await.get(batch_id).cloned()
    }

    /// Cancel a running batch
    pub async fn cancel_batch(&self, batch_id: &str) -> Result<(), ConversionError> {
        if let Some(mut progress) = self.active_batches.write().await.get_mut(batch_id) {
            progress.status = BatchStatus::Cancelled;
            Ok(())
        } else {
            Err(ConversionError::ValidationError(format!("Batch {} not found", batch_id)))
        }
    }

    /// Pause a running batch
    pub async fn pause_batch(&self, batch_id: &str) -> Result<(), ConversionError> {
        if let Some(mut progress) = self.active_batches.write().await.get_mut(batch_id) {
            if progress.status == BatchStatus::Running {
                progress.status = BatchStatus::Paused;
                Ok(())
            } else {
                Err(ConversionError::ValidationError("Batch is not running".to_string()))
            }
        } else {
            Err(ConversionError::ValidationError(format!("Batch {} not found", batch_id)))
        }
    }

    /// Resume a paused batch
    pub async fn resume_batch(&self, batch_id: &str) -> Result<(), ConversionError> {
        if let Some(mut progress) = self.active_batches.write().await.get_mut(batch_id) {
            if progress.status == BatchStatus::Paused {
                progress.status = BatchStatus::Running;
                Ok(())
            } else {
                Err(ConversionError::ValidationError("Batch is not paused".to_string()))
            }
        } else {
            Err(ConversionError::ValidationError(format!("Batch {} not found", batch_id)))
        }
    }

    /// Get comprehensive batch processor statistics
    pub fn get_processor_stats(&self) -> BatchProcessorStats {
        BatchProcessorStats {
            total_batches_processed: self.metrics.total_batches_processed.load(Ordering::Relaxed),
            total_documents_processed: self.metrics.total_documents_processed.load(Ordering::Relaxed),
            average_processing_time_ms: {
                let total_time = self.metrics.total_processing_time_ms.load(Ordering::Relaxed);
                let total_docs = self.metrics.total_documents_processed.load(Ordering::Relaxed);
                if total_docs > 0 { total_time as f64 / total_docs as f64 } else { 0.0 }
            },
            total_errors: self.metrics.total_errors.load(Ordering::Relaxed),
            total_retries: self.metrics.total_retries.load(Ordering::Relaxed),
            error_rate_percent: {
                let errors = self.metrics.total_errors.load(Ordering::Relaxed);
                let total = self.metrics.total_documents_processed.load(Ordering::Relaxed);
                if total > 0 { (errors as f64 / total as f64) * 100.0 } else { 0.0 }
            },
            active_batches: 0, // Will be calculated from active_batches map
        }
    }

    fn validate_batch_request(&self, request: &BatchRequest) -> Result<(), ConversionError> {
        if request.documents.is_empty() {
            return Err(ConversionError::ValidationError("Batch cannot be empty".to_string()));
        }

        if request.documents.len() > self.config.max_batch_size {
            return Err(ConversionError::ValidationError(
                format!("Batch size {} exceeds maximum {}", 
                       request.documents.len(), self.config.max_batch_size)
            ));
        }

        Ok(())
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct BatchProcessorStats {
    pub total_batches_processed: usize,
    pub total_documents_processed: u64,
    pub average_processing_time_ms: f64,
    pub total_errors: usize,
    pub total_retries: usize,
    pub error_rate_percent: f64,
    pub active_batches: usize,
}

impl Clone for EnhancedBatchProcessor {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            processor: self.processor.clone(),
            active_batches: self.active_batches.clone(),
            progress_senders: self.progress_senders.clone(),
            metrics: self.metrics.clone(),
        }
    }
}

impl EnhancedBatchProcessor {
    /// Internal batch processing implementation
    async fn process_batch_internal(&self, request: BatchRequest) {
        let batch_id = request.batch_id.clone();
        let start_time = Instant::now();

        // Update batch status to running
        {
            let mut batches = self.active_batches.write().await;
            if let Some(progress) = batches.get_mut(&batch_id) {
                progress.status = BatchStatus::Running;
                self.send_progress_update(&batch_id, progress.clone()).await;
            }
        }

        // Create semaphore for concurrency control
        let semaphore = Arc::new(Semaphore::new(self.config.max_concurrent));
        let mut handles = Vec::new();

        // Process documents with controlled concurrency
        for (index, document) in request.documents.into_iter().enumerate() {
            let permit = semaphore.clone().acquire_owned().await.unwrap();
            let processor = self.processor.clone();
            let batch_processor = self.clone();
            let batch_id_clone = batch_id.clone();
            let config = self.config.clone();

            let handle = tokio::spawn(async move {
                let result = batch_processor.process_document_with_retry(
                    document,
                    index,
                    &batch_id_clone,
                    processor,
                    config
                ).await;
                drop(permit);
                result
            });

            handles.push(handle);
        }

        // Wait for all documents to complete
        let mut completed = 0;
        let mut failed = 0;

        for handle in handles {
            match handle.await {
                Ok(success) => {
                    if success { completed += 1; } else { failed += 1; }
                }
                Err(_) => failed += 1,
            }
        }

        // Update final batch status
        let final_status = if failed == 0 { BatchStatus::Completed } else { BatchStatus::Failed };
        let elapsed = start_time.elapsed();

        {
            let mut batches = self.active_batches.write().await;
            if let Some(progress) = batches.get_mut(&batch_id) {
                progress.status = final_status;
                progress.elapsed_time_ms = elapsed.as_millis() as u64;
                progress.progress_percentage = 100.0;
                progress.estimated_time_remaining_ms = Some(0);

                // Calculate final metrics
                self.calculate_final_metrics(progress);

                self.send_progress_update(&batch_id, progress.clone()).await;
            }
        }

        // Update global metrics
        self.metrics.total_batches_processed.fetch_add(1, Ordering::Relaxed);
        self.metrics.total_processing_time_ms.fetch_add(elapsed.as_millis() as u64, Ordering::Relaxed);

        // Clean up
        self.cleanup_batch(&batch_id).await;
    }

    /// Process a single document with retry logic
    async fn process_document_with_retry(
        &self,
        mut document: ConversionRequest,
        index: usize,
        batch_id: &str,
        processor: Arc<ConcurrentProcessorV2>,
        config: BatchProcessorConfig,
    ) -> bool {
        let document_id = document.id.clone();
        let mut attempts = 0;

        while attempts < config.max_retries {
            attempts += 1;

            // Update document status
            self.update_document_status(
                batch_id,
                &document_id,
                if attempts == 1 { DocumentProcessingStatus::Processing } else { DocumentProcessingStatus::Retrying },
                attempts,
                None
            ).await;

            let start_time = Instant::now();

            // Process document with timeout
            let result = tokio::time::timeout(
                config.document_timeout,
                processor.process_single(document.clone())
            ).await;

            let processing_time = start_time.elapsed();

            match result {
                Ok(response) => {
                    match response.result {
                        Ok(_) => {
                            // Success
                            self.update_document_status(
                                batch_id,
                                &document_id,
                                DocumentProcessingStatus::Completed,
                                attempts,
                                Some(processing_time.as_millis() as u64)
                            ).await;

                            self.update_batch_progress(batch_id, true).await;
                            self.metrics.total_documents_processed.fetch_add(1, Ordering::Relaxed);
                            return true;
                        }
                        Err(error) => {
                            // Document processing failed
                            if attempts >= config.max_retries {
                                self.update_document_status(
                                    batch_id,
                                    &document_id,
                                    DocumentProcessingStatus::Failed,
                                    attempts,
                                    Some(processing_time.as_millis() as u64)
                                ).await;

                                self.update_batch_progress(batch_id, false).await;
                                self.metrics.total_errors.fetch_add(1, Ordering::Relaxed);
                                return false;
                            } else {
                                // Retry
                                self.metrics.total_retries.fetch_add(1, Ordering::Relaxed);
                                tokio::time::sleep(config.retry_delay).await;
                            }
                        }
                    }
                }
                Err(_) => {
                    // Timeout
                    if attempts >= config.max_retries {
                        self.update_document_status(
                            batch_id,
                            &document_id,
                            DocumentProcessingStatus::Failed,
                            attempts,
                            Some(config.document_timeout.as_millis() as u64)
                        ).await;

                        self.update_batch_progress(batch_id, false).await;
                        self.metrics.total_errors.fetch_add(1, Ordering::Relaxed);
                        return false;
                    } else {
                        self.metrics.total_retries.fetch_add(1, Ordering::Relaxed);
                        tokio::time::sleep(config.retry_delay).await;
                    }
                }
            }
        }

        false
    }

    /// Update document status within a batch
    async fn update_document_status(
        &self,
        batch_id: &str,
        document_id: &str,
        status: DocumentProcessingStatus,
        attempts: u32,
        processing_time_ms: Option<u64>,
    ) {
        let mut batches = self.active_batches.write().await;
        if let Some(progress) = batches.get_mut(batch_id) {
            if let Some(doc) = progress.documents.iter_mut().find(|d| d.id == document_id) {
                doc.status = status;
                doc.attempts = attempts;
                if let Some(time) = processing_time_ms {
                    doc.processing_time_ms = time;
                }
            }
        }
    }

    /// Update overall batch progress
    async fn update_batch_progress(&self, batch_id: &str, success: bool) {
        let mut batches = self.active_batches.write().await;
        if let Some(progress) = batches.get_mut(batch_id) {
            if success {
                progress.completed_documents += 1;
            } else {
                progress.failed_documents += 1;
            }

            let total_processed = progress.completed_documents + progress.failed_documents;
            progress.progress_percentage = (total_processed as f64 / progress.total_documents as f64) * 100.0;

            // Calculate throughput and ETA
            if progress.elapsed_time_ms > 0 {
                progress.throughput_docs_per_second = (total_processed as f64 / progress.elapsed_time_ms as f64) * 1000.0;

                if progress.throughput_docs_per_second > 0.0 {
                    let remaining_docs = progress.total_documents - total_processed;
                    progress.estimated_time_remaining_ms = Some(
                        (remaining_docs as f64 / progress.throughput_docs_per_second * 1000.0) as u64
                    );
                }
            }

            self.send_progress_update(batch_id, progress.clone()).await;
        }
    }

    /// Send progress update to subscribers
    async fn send_progress_update(&self, batch_id: &str, progress: BatchProgress) {
        let senders = self.progress_senders.read().await;
        if let Some(sender) = senders.get(batch_id) {
            let _ = sender.send(progress);
        }
    }

    /// Calculate final batch metrics
    fn calculate_final_metrics(&self, progress: &mut BatchProgress) {
        let total_input: u64 = progress.documents.iter().map(|d| d.size_bytes as u64).sum();
        let total_output: u64 = progress.documents.iter()
            .filter_map(|d| d.result_size_bytes.map(|s| s as u64))
            .sum();

        let total_processing_time: u64 = progress.documents.iter()
            .map(|d| d.processing_time_ms)
            .sum();

        let completed_docs = progress.documents.iter()
            .filter(|d| d.status == DocumentProcessingStatus::Completed)
            .count();

        progress.metrics = BatchMetrics {
            total_input_bytes: total_input,
            total_output_bytes: total_output,
            average_processing_time_ms: if completed_docs > 0 {
                total_processing_time as f64 / completed_docs as f64
            } else { 0.0 },
            peak_memory_usage_mb: 0.0, // Would need system monitoring
            cpu_utilization_percent: 0.0, // Would need system monitoring
            error_rate_percent: (progress.failed_documents as f64 / progress.total_documents as f64) * 100.0,
            retry_rate_percent: {
                let total_retries: u32 = progress.documents.iter().map(|d| d.attempts.saturating_sub(1)).sum();
                (total_retries as f64 / progress.total_documents as f64) * 100.0
            },
            compression_ratio: if total_input > 0 { total_output as f64 / total_input as f64 } else { 1.0 },
        };
    }

    /// Clean up completed batch
    async fn cleanup_batch(&self, batch_id: &str) {
        // Remove from progress senders after a delay to allow final updates
        tokio::time::sleep(Duration::from_secs(5)).await;
        self.progress_senders.write().await.remove(batch_id);

        // Keep batch progress for a while for status queries
        tokio::time::sleep(Duration::from_secs(300)).await; // 5 minutes
        self.active_batches.write().await.remove(batch_id);
    }
}
