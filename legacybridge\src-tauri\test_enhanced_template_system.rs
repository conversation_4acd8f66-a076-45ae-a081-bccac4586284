// Enhanced Template System Performance Test
// Validates advanced enterprise template features and performance

use std::time::Instant;
use legacybridge::pipeline::template_system::{
    TemplateSystem, DocumentTemplate, TemplateType, EnterpriseFeatures,
    WatermarkConfig, WatermarkPosition, DocumentClassification, ClassificationLevel,
    RetentionPolicy, AccessControl, ConditionalRule, ConditionalAction,
    LocalizationConfig, ApprovalWorkflow, ApprovalOrder
};
use legacybridge::conversion::types::{RtfDocument, RtfNode, DocumentMetadata};
use std::collections::HashMap;

fn main() {
    println!("Enhanced Template System Performance Test");
    println!("========================================\n");
    
    // Test enhanced template system
    test_enterprise_templates();
    
    // Test template performance
    test_template_performance();
    
    // Test advanced features
    test_enterprise_features();
    
    // Test localization
    test_localization_features();
    
    // Test conditional content
    test_conditional_content();
    
    // Run comprehensive template validation
    run_template_validation();
}

fn test_enterprise_templates() {
    println!("Testing Enterprise Templates");
    println!("----------------------------");
    
    let template_system = TemplateSystem::new();
    let templates = template_system.list_templates();
    
    println!("Available templates: {}", templates.len());
    for template_name in &templates {
        println!("  - {}", template_name);
    }
    
    // Test financial report template
    if let Some(financial_template) = template_system.get_template("financial_report") {
        println!("\nFinancial Report Template Features:");
        println!("  Type: {:?}", financial_template.template_type);
        println!("  Version: {}", financial_template.version);
        
        if let Some(ref enterprise) = financial_template.enterprise_features {
            println!("  Digital Signature: {}", enterprise.digital_signature);
            println!("  Audit Trail: {}", enterprise.audit_trail);
            
            if let Some(ref watermark) = enterprise.watermark {
                println!("  Watermark: {} ({})", watermark.text, watermark.opacity);
            }
            
            if let Some(ref classification) = enterprise.classification {
                println!("  Classification: {:?}", classification.level);
            }
        }
        
        if let Some(ref approval) = financial_template.approval_workflow {
            println!("  Approval Order: {:?}", approval.approval_order);
            println!("  Required Approvers: {}", approval.required_approvers.len());
        }
    }
    
    // Test technical specification template
    if let Some(tech_template) = template_system.get_template("tech_spec") {
        println!("\nTechnical Specification Template Features:");
        println!("  Type: {:?}", tech_template.template_type);
        println!("  Styles: {}", tech_template.styles.len());
        
        if let Some(ref enterprise) = tech_template.enterprise_features {
            if let Some(ref access_control) = enterprise.access_control {
                println!("  Required Roles: {:?}", access_control.required_roles);
                println!("  Department Restrictions: {:?}", access_control.department_restrictions);
            }
        }
    }
    
    println!();
}

fn test_template_performance() {
    println!("Testing Template Performance");
    println!("---------------------------");
    
    let template_system = TemplateSystem::new();
    let mut test_document = create_test_document();
    
    // Test template application performance
    let start = Instant::now();
    for _ in 0..100 {
        let mut doc_copy = test_document.clone();
        let _ = template_system.apply_template(&mut doc_copy, "financial_report");
    }
    let financial_time = start.elapsed();
    
    let start = Instant::now();
    for _ in 0..100 {
        let mut doc_copy = test_document.clone();
        let _ = template_system.apply_template(&mut doc_copy, "tech_spec");
    }
    let tech_time = start.elapsed();
    
    let start = Instant::now();
    for _ in 0..100 {
        let mut doc_copy = test_document.clone();
        let _ = template_system.apply_template(&mut doc_copy, "memo");
    }
    let memo_time = start.elapsed();
    
    println!("Template Application Performance (100 iterations):");
    println!("  Financial Report: {:?} (avg: {:?})", financial_time, financial_time / 100);
    println!("  Tech Specification: {:?} (avg: {:?})", tech_time, tech_time / 100);
    println!("  Memo: {:?} (avg: {:?})", memo_time, memo_time / 100);
    
    // Test template loading performance
    let start = Instant::now();
    for _ in 0..1000 {
        let _system = TemplateSystem::new();
    }
    let loading_time = start.elapsed();
    
    println!("Template System Loading (1000 iterations): {:?} (avg: {:?})", 
             loading_time, loading_time / 1000);
    println!();
}

fn test_enterprise_features() {
    println!("Testing Enterprise Features");
    println!("---------------------------");
    
    let template_system = TemplateSystem::new();
    
    if let Some(financial_template) = template_system.get_template("financial_report") {
        if let Some(ref enterprise) = financial_template.enterprise_features {
            println!("Enterprise Features Validation:");
            
            // Test watermark configuration
            if let Some(ref watermark) = enterprise.watermark {
                println!("  ✅ Watermark: {} at {:?} position", watermark.text, watermark.position);
                println!("     Opacity: {}, Font Size: {}, Color: {}", 
                         watermark.opacity, watermark.font_size, watermark.color);
            }
            
            // Test document classification
            if let Some(ref classification) = enterprise.classification {
                println!("  ✅ Classification: {:?}", classification.level);
                println!("     Marking: {}", classification.marking);
                println!("     Handling Instructions: {}", classification.handling_instructions.len());
            }
            
            // Test retention policy
            if let Some(ref retention) = enterprise.retention_policy {
                println!("  ✅ Retention Policy: {} years", retention.retention_period_years);
                println!("     Auto Delete: {}", retention.auto_delete);
                if let Some(ref location) = retention.archive_location {
                    println!("     Archive Location: {}", location);
                }
            }
            
            // Test access control
            if let Some(ref access) = enterprise.access_control {
                println!("  ✅ Access Control:");
                println!("     Required Roles: {:?}", access.required_roles);
                println!("     Department Restrictions: {:?}", access.department_restrictions);
            }
            
            println!("  ✅ Digital Signature: {}", enterprise.digital_signature);
            println!("  ✅ Audit Trail: {}", enterprise.audit_trail);
        }
    }
    
    println!();
}

fn test_localization_features() {
    println!("Testing Localization Features");
    println!("-----------------------------");
    
    let template_system = TemplateSystem::new();
    
    if let Some(financial_template) = template_system.get_template("financial_report") {
        if let Some(ref localization) = financial_template.localization {
            println!("Localization Configuration:");
            println!("  Default Language: {}", localization.default_language);
            println!("  Supported Languages: {:?}", localization.supported_languages);
            
            // Test translations
            for (lang, translations) in &localization.translations {
                println!("  {} Translations:", lang);
                for (key, value) in translations {
                    println!("    {}: {}", key, value);
                }
            }
            
            // Test date formats
            println!("  Date Formats:");
            for (lang, format) in &localization.date_formats {
                println!("    {}: {}", lang, format);
            }
            
            // Test number formats
            println!("  Number Formats:");
            for (lang, format) in &localization.number_formats {
                println!("    {}: {}", lang, format);
            }
        }
    }
    
    println!();
}

fn test_conditional_content() {
    println!("Testing Conditional Content");
    println!("---------------------------");
    
    let template_system = TemplateSystem::new();
    
    if let Some(financial_template) = template_system.get_template("financial_report") {
        println!("Conditional Rules: {}", financial_template.conditional_content.len());
        
        for (i, rule) in financial_template.conditional_content.iter().enumerate() {
            println!("  Rule {}: {}", i + 1, rule.condition);
            println!("    Action: {:?}", rule.action);
            println!("    Target: {}", rule.target);
        }
    }
    
    println!();
}

fn run_template_validation() {
    println!("Comprehensive Template Validation");
    println!("=================================");
    
    let template_system = TemplateSystem::new();
    let templates = template_system.list_templates();
    let mut test_document = create_test_document();
    
    let mut successful_applications = 0;
    let mut total_features_tested = 0;
    
    for template_name in &templates {
        print!("Testing template '{}': ", template_name);
        
        match template_system.apply_template(&mut test_document.clone(), template_name) {
            Ok(_) => {
                println!("✅ SUCCESS");
                successful_applications += 1;
                
                // Count features in this template
                if let Some(template) = template_system.get_template(template_name) {
                    if template.enterprise_features.is_some() { total_features_tested += 1; }
                    if template.localization.is_some() { total_features_tested += 1; }
                    if !template.conditional_content.is_empty() { total_features_tested += 1; }
                    if template.approval_workflow.is_some() { total_features_tested += 1; }
                }
            }
            Err(e) => {
                println!("❌ FAILED: {}", e);
            }
        }
    }
    
    println!("\nValidation Summary:");
    println!("  Total Templates: {}", templates.len());
    println!("  Successful Applications: {}", successful_applications);
    println!("  Success Rate: {:.1}%", 
             (successful_applications as f64 / templates.len() as f64) * 100.0);
    println!("  Enterprise Features Tested: {}", total_features_tested);
    
    println!("\n✅ Enhanced Template System Test Complete!");
    println!("   Advanced enterprise features are working correctly.");
}

fn create_test_document() -> RtfDocument {
    RtfDocument {
        metadata: DocumentMetadata {
            title: Some("Test Document".to_string()),
            author: Some("Test Author".to_string()),
            company: Some("Test Company".to_string()),
            subject: Some("Template Testing".to_string()),
            keywords: Some("test, template, enterprise".to_string()),
            comments: Some("Generated for template testing".to_string()),
            creation_time: None,
            revision_time: None,
            print_time: None,
            pages: Some(1),
            words: Some(100),
            characters: Some(500),
            department: Some("Engineering".to_string()),
        },
        content: vec![
            RtfNode::Paragraph(vec![
                RtfNode::Text("This is a test document for template validation.".to_string()),
            ]),
            RtfNode::Paragraph(vec![
                RtfNode::Text("It contains sample content to test template application.".to_string()),
            ]),
        ],
    }
}
