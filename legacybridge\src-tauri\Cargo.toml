[package]
name = "legacybridge"
version = "0.0.0"
description = "LegacyBridge - Convert legacy code to modern frameworks"
authors = ["you"]
edition = "2021"

[lib]
name = "legacybridge"
path = "src/lib.rs"
crate-type = ["cdylib", "rlib"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1", features = [] }

[dependencies]
tauri = { version = "1", features = ["clipboard-write-text", "dialog-open", "dialog-save", "fs-create-dir", "fs-exists", "fs-read-dir", "fs-read-file", "fs-write-file", "process-exit", "system-tray", "window-center", "window-close", "window-hide", "window-maximize", "window-minimize", "window-request-user-attention", "window-set-cursor-icon", "window-set-cursor-visible", "window-set-decorations", "window-set-focus", "window-set-fullscreen", "window-set-max-size", "window-set-min-size", "window-set-resizable", "window-set-size", "window-set-title", "window-show", "window-start-dragging", "window-unmaximize", "window-unminimize"], optional = true }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
thiserror = "1.0"
base64 = "0.21"
regex = "1.10"
chrono = "0.4"
pulldown-cmark = "0.9"
rayon = "1.8"
ahash = "0.8"
smallvec = "1.11"
bumpalo = "3.14"
crossbeam-channel = "0.5"
crossbeam-deque = "0.8"
num_cpus = "1.16"
tokio = { version = "1.35", features = ["rt-multi-thread", "time", "macros"], optional = true }
lazy_static = "1.4"
parking_lot = "0.12"
libc = "0.2"
once_cell = "1.19"
tracing = "0.1"
lru = "0.12"

[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
proptest = "1.4"
pretty_assertions = "1.4"
test-case = "3.3"
rstest = "0.24"
fake = "3.0"
quickcheck = "1.0"

[[bench]]
name = "conversion_bench"
harness = false

[[bench]]
name = "performance_benchmarks"
harness = false
path = "benches/performance_benchmarks.rs"

[[bench]]
name = "thread_pool_bench"
harness = false

[[bench]]
name = "simd_benchmarks"
harness = false
path = "src/conversion/simd_benchmarks.rs"

[profile.release]
lto = true
codegen-units = 1
opt-level = 3
strip = true
panic = "abort"
overflow-checks = false

[profile.bench]
debug = true

[profile.release-min]
inherits = "release"
opt-level = "s"
lto = "fat"
strip = true
panic = "abort"

[[bin]]
name = "validate_md_rtf"
path = "validate_md_rtf.rs"

[[example]]
name = "thread_pool_demo"
path = "examples/thread_pool_demo.rs"

[[bin]]
name = "test_simd_performance"
path = "test_simd_performance.rs"

[[bin]]
name = "test_memory_pool_optimization"
path = "test_memory_pool_optimization.rs"

[[bin]]
name = "test_enhanced_template_system"
path = "test_enhanced_template_system.rs"

[features]
default = ["tauri-app", "legacy-formats"]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
tauri-app = ["tauri", "tokio", "custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]
# Feature for building as standalone DLL without Tauri dependencies
dll-export = []
# Legacy format support features
legacy-formats = ["format-doc", "format-wordperfect", "format-dbase", "format-wordstar", "format-lotus"]
format-doc = []
format-wordperfect = []
format-dbase = []
format-wordstar = []
format-lotus = []
# VB6/VFP9 FFI interface
vb6-ffi = ["legacy-formats"]