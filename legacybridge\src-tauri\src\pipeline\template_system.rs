// Template System - Enterprise document templates
//
// This module provides a template system for applying enterprise-specific
// formatting and structure to converted documents.

use crate::conversion::types::{
    ConversionError, ConversionResult, RtfDocument, RtfNode, DocumentMetadata,
    FontInfo, FontFamily,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;

/// Template definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentTemplate {
    /// Template name
    pub name: String,
    /// Template description
    pub description: String,
    /// Template version
    pub version: String,
    /// Template type (memo, report, letter, etc.)
    pub template_type: TemplateType,
    /// Header configuration
    pub header: Option<HeaderConfig>,
    /// Footer configuration
    pub footer: Option<FooterConfig>,
    /// Style definitions
    pub styles: HashMap<String, StyleDefinition>,
    /// Document metadata overrides
    pub metadata_overrides: Option<MetadataOverrides>,
    /// Content transformations
    pub transformations: Vec<ContentTransformation>,
    /// Legacy system compatibility settings
    pub legacy_settings: Option<LegacySettings>,
    /// Advanced enterprise features
    pub enterprise_features: Option<EnterpriseFeatures>,
    /// Conditional content rules
    pub conditional_content: Vec<ConditionalRule>,
    /// Multi-language support
    pub localization: Option<LocalizationConfig>,
    /// Approval workflow settings
    pub approval_workflow: Option<ApprovalWorkflow>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TemplateType {
    Memo,
    Report,
    Letter,
    Invoice,
    Contract,
    Manual,
    Proposal,
    Specification,
    UserGuide,
    TechnicalDocument,
    FinancialReport,
    LegalDocument,
    MarketingMaterial,
    TrainingMaterial,
    PolicyDocument,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeaderConfig {
    /// Header content template
    pub content: String,
    /// Variables to replace in content
    pub variables: HashMap<String, String>,
    /// Alignment
    pub alignment: Alignment,
    /// Include page numbers
    pub include_page_numbers: bool,
    /// Include date
    pub include_date: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FooterConfig {
    /// Footer content template
    pub content: String,
    /// Variables to replace in content
    pub variables: HashMap<String, String>,
    /// Alignment
    pub alignment: Alignment,
    /// Include page numbers
    pub include_page_numbers: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Alignment {
    Left,
    Center,
    Right,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StyleDefinition {
    /// Style name
    pub name: String,
    /// Font settings
    pub font: Option<FontSettings>,
    /// Paragraph settings
    pub paragraph: Option<ParagraphSettings>,
    /// List settings
    pub list: Option<ListSettings>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FontSettings {
    pub family: String,
    pub size: i32,
    pub bold: bool,
    pub italic: bool,
    pub underline: bool,
    pub color: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParagraphSettings {
    pub alignment: Alignment,
    pub line_spacing: f32,
    pub space_before: i32,
    pub space_after: i32,
    pub first_line_indent: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ListSettings {
    pub list_type: String,
    pub indent_per_level: i32,
    pub numbering_format: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetadataOverrides {
    pub default_font: Option<String>,
    pub author: Option<String>,
    pub company: Option<String>,
    pub department: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContentTransformation {
    /// Transformation type
    pub transform_type: TransformationType,
    /// Target elements (heading, paragraph, list, etc.)
    pub target: TransformTarget,
    /// Transformation parameters
    pub parameters: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransformationType {
    /// Add prefix or suffix
    AddWrapper,
    /// Replace content matching pattern
    ReplacePattern,
    /// Apply style
    ApplyStyle,
    /// Insert element
    InsertElement,
    /// Restructure content
    Restructure,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransformTarget {
    AllHeadings,
    HeadingLevel(u8),
    AllParagraphs,
    FirstParagraph,
    AllLists,
    Tables,
    Custom(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacySettings {
    /// VB6 compatibility mode
    pub vb6_compatible: bool,
    /// VFP9 compatibility mode
    pub vfp9_compatible: bool,
    /// Use legacy encoding
    pub use_legacy_encoding: bool,
    /// Legacy date format
    pub date_format: Option<String>,
    /// Legacy number format
    pub number_format: Option<String>,
}

/// Advanced enterprise features
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnterpriseFeatures {
    /// Digital signature support
    pub digital_signature: bool,
    /// Watermark configuration
    pub watermark: Option<WatermarkConfig>,
    /// Document classification
    pub classification: Option<DocumentClassification>,
    /// Retention policy
    pub retention_policy: Option<RetentionPolicy>,
    /// Access control
    pub access_control: Option<AccessControl>,
    /// Audit trail
    pub audit_trail: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WatermarkConfig {
    pub text: String,
    pub opacity: f32,
    pub position: WatermarkPosition,
    pub font_size: u32,
    pub color: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WatermarkPosition {
    Center,
    TopLeft,
    TopRight,
    BottomLeft,
    BottomRight,
    Diagonal,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentClassification {
    pub level: ClassificationLevel,
    pub marking: String,
    pub handling_instructions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ClassificationLevel {
    Public,
    Internal,
    Confidential,
    Restricted,
    Secret,
    TopSecret,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetentionPolicy {
    pub retention_period_years: u32,
    pub auto_delete: bool,
    pub archive_location: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessControl {
    pub required_roles: Vec<String>,
    pub department_restrictions: Vec<String>,
    pub geographic_restrictions: Vec<String>,
}

/// Conditional content rules
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConditionalRule {
    pub condition: String,
    pub action: ConditionalAction,
    pub target: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConditionalAction {
    Include,
    Exclude,
    Replace(String),
    Modify(HashMap<String, String>),
}

/// Multi-language support
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalizationConfig {
    pub default_language: String,
    pub supported_languages: Vec<String>,
    pub translations: HashMap<String, HashMap<String, String>>,
    pub date_formats: HashMap<String, String>,
    pub number_formats: HashMap<String, String>,
}

/// Approval workflow
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApprovalWorkflow {
    pub required_approvers: Vec<String>,
    pub approval_order: ApprovalOrder,
    pub auto_route: bool,
    pub deadline_days: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ApprovalOrder {
    Sequential,
    Parallel,
    Hierarchical,
}

/// Template system implementation
pub struct TemplateSystem {
    templates: HashMap<String, DocumentTemplate>,
    template_dir: Option<String>,
}

impl TemplateSystem {
    pub fn new() -> Self {
        let mut system = Self {
            templates: HashMap::new(),
            template_dir: None,
        };
        
        // Load built-in templates
        system.load_builtin_templates();

        // Load enterprise templates
        system.load_enterprise_templates();

        system
    }

    /// Load built-in enterprise templates
    fn load_builtin_templates(&mut self) {
        // Memo template
        self.templates.insert(
            "memo".to_string(),
            DocumentTemplate {
                name: "Enterprise Memo".to_string(),
                description: "Standard enterprise memo template".to_string(),
                version: "1.0.0".to_string(),
                template_type: TemplateType::Memo,
                header: Some(HeaderConfig {
                    content: "{{company}} - Internal Memorandum".to_string(),
                    variables: HashMap::from([
                        ("company".to_string(), "ACME Corporation".to_string()),
                    ]),
                    alignment: Alignment::Center,
                    include_page_numbers: true,
                    include_date: true,
                }),
                footer: Some(FooterConfig {
                    content: "Confidential - Page {{page}}".to_string(),
                    variables: HashMap::new(),
                    alignment: Alignment::Center,
                    include_page_numbers: true,
                }),
                styles: self.create_memo_styles(),
                metadata_overrides: Some(MetadataOverrides {
                    default_font: Some("Arial".to_string()),
                    author: None,
                    company: Some("ACME Corporation".to_string()),
                    department: None,
                }),
                transformations: vec![
                    ContentTransformation {
                        transform_type: TransformationType::ApplyStyle,
                        target: TransformTarget::HeadingLevel(1),
                        parameters: HashMap::from([
                            ("style".to_string(), "memo-heading".to_string()),
                        ]),
                    },
                ],
                legacy_settings: Some(LegacySettings {
                    vb6_compatible: true,
                    vfp9_compatible: true,
                    use_legacy_encoding: false,
                    date_format: Some("MM/DD/YYYY".to_string()),
                    number_format: Some("#,##0.00".to_string()),
                }),
            },
        );

        // Report template
        self.templates.insert(
            "report".to_string(),
            DocumentTemplate {
                name: "Enterprise Report".to_string(),
                description: "Standard enterprise report template".to_string(),
                version: "1.0.0".to_string(),
                template_type: TemplateType::Report,
                header: Some(HeaderConfig {
                    content: "{{title}} - {{date}}".to_string(),
                    variables: HashMap::new(),
                    alignment: Alignment::Right,
                    include_page_numbers: true,
                    include_date: true,
                }),
                footer: Some(FooterConfig {
                    content: "{{company}} - Proprietary Information".to_string(),
                    variables: HashMap::from([
                        ("company".to_string(), "ACME Corporation".to_string()),
                    ]),
                    alignment: Alignment::Center,
                    include_page_numbers: true,
                }),
                styles: self.create_report_styles(),
                metadata_overrides: Some(MetadataOverrides {
                    default_font: Some("Times New Roman".to_string()),
                    author: None,
                    company: Some("ACME Corporation".to_string()),
                    department: None,
                }),
                transformations: vec![
                    ContentTransformation {
                        transform_type: TransformationType::AddWrapper,
                        target: TransformTarget::FirstParagraph,
                        parameters: HashMap::from([
                            ("prefix".to_string(), "Executive Summary: ".to_string()),
                        ]),
                    },
                ],
                legacy_settings: None,
                enterprise_features: None,
                conditional_content: vec![],
                localization: None,
                approval_workflow: None,
            },
        );
    }

    /// Load enterprise templates with advanced features
    fn load_enterprise_templates(&mut self) {
        // Financial Report template
        self.templates.insert(
            "financial_report".to_string(),
            DocumentTemplate {
                name: "Financial Report".to_string(),
                description: "Enterprise financial reporting template with compliance features".to_string(),
                version: "2.0.0".to_string(),
                template_type: TemplateType::FinancialReport,
                header: Some(HeaderConfig {
                    content: "{{company}} - {{report_type}} - {{period}}".to_string(),
                    variables: HashMap::from([
                        ("company".to_string(), "ACME Corporation".to_string()),
                        ("report_type".to_string(), "Quarterly Financial Report".to_string()),
                    ]),
                    alignment: Alignment::Center,
                    include_page_numbers: true,
                    include_date: true,
                }),
                footer: Some(FooterConfig {
                    content: "{{classification}} - Page {{page}} of {{total_pages}}".to_string(),
                    variables: HashMap::from([
                        ("classification".to_string(), "CONFIDENTIAL".to_string()),
                    ]),
                    alignment: Alignment::Center,
                    include_page_numbers: true,
                }),
                styles: self.create_financial_styles(),
                metadata_overrides: Some(MetadataOverrides {
                    default_font: Some("Arial".to_string()),
                    author: Some("Finance Department".to_string()),
                    company: Some("ACME Corporation".to_string()),
                    department: Some("Finance".to_string()),
                }),
                transformations: vec![
                    ContentTransformation {
                        transform_type: TransformationType::AddWrapper,
                        target: TransformTarget::FirstParagraph,
                        parameters: HashMap::from([
                            ("prefix".to_string(), "EXECUTIVE SUMMARY\n".to_string()),
                        ]),
                    },
                ],
                legacy_settings: None,
                enterprise_features: Some(EnterpriseFeatures {
                    digital_signature: true,
                    watermark: Some(WatermarkConfig {
                        text: "CONFIDENTIAL".to_string(),
                        opacity: 0.3,
                        position: WatermarkPosition::Diagonal,
                        font_size: 48,
                        color: "#FF0000".to_string(),
                    }),
                    classification: Some(DocumentClassification {
                        level: ClassificationLevel::Confidential,
                        marking: "CONFIDENTIAL - FINANCIAL DATA".to_string(),
                        handling_instructions: vec![
                            "Do not distribute outside authorized personnel".to_string(),
                            "Destroy after retention period".to_string(),
                        ],
                    }),
                    retention_policy: Some(RetentionPolicy {
                        retention_period_years: 7,
                        auto_delete: false,
                        archive_location: Some("secure_archive".to_string()),
                    }),
                    access_control: Some(AccessControl {
                        required_roles: vec!["finance_manager".to_string(), "executive".to_string()],
                        department_restrictions: vec!["Finance".to_string(), "Executive".to_string()],
                        geographic_restrictions: vec![],
                    }),
                    audit_trail: true,
                }),
                conditional_content: vec![
                    ConditionalRule {
                        condition: "quarter == 'Q4'".to_string(),
                        action: ConditionalAction::Include,
                        target: "annual_summary".to_string(),
                    },
                ],
                localization: Some(LocalizationConfig {
                    default_language: "en-US".to_string(),
                    supported_languages: vec!["en-US".to_string(), "es-ES".to_string(), "fr-FR".to_string()],
                    translations: HashMap::from([
                        ("en-US".to_string(), HashMap::from([
                            ("title".to_string(), "Financial Report".to_string()),
                            ("confidential".to_string(), "CONFIDENTIAL".to_string()),
                        ])),
                        ("es-ES".to_string(), HashMap::from([
                            ("title".to_string(), "Informe Financiero".to_string()),
                            ("confidential".to_string(), "CONFIDENCIAL".to_string()),
                        ])),
                    ]),
                    date_formats: HashMap::from([
                        ("en-US".to_string(), "%m/%d/%Y".to_string()),
                        ("es-ES".to_string(), "%d/%m/%Y".to_string()),
                    ]),
                    number_formats: HashMap::from([
                        ("en-US".to_string(), "1,234.56".to_string()),
                        ("es-ES".to_string(), "1.234,56".to_string()),
                    ]),
                }),
                approval_workflow: Some(ApprovalWorkflow {
                    required_approvers: vec!["cfo".to_string(), "ceo".to_string()],
                    approval_order: ApprovalOrder::Sequential,
                    auto_route: true,
                    deadline_days: Some(5),
                }),
            },
        );

        // Technical Specification template
        self.templates.insert(
            "tech_spec".to_string(),
            DocumentTemplate {
                name: "Technical Specification".to_string(),
                description: "Enterprise technical specification template".to_string(),
                version: "2.0.0".to_string(),
                template_type: TemplateType::Specification,
                header: Some(HeaderConfig {
                    content: "{{project}} - Technical Specification v{{version}}".to_string(),
                    variables: HashMap::new(),
                    alignment: Alignment::Left,
                    include_page_numbers: true,
                    include_date: true,
                }),
                footer: Some(FooterConfig {
                    content: "{{company}} - {{classification}}".to_string(),
                    variables: HashMap::from([
                        ("company".to_string(), "ACME Corporation".to_string()),
                        ("classification".to_string(), "INTERNAL USE ONLY".to_string()),
                    ]),
                    alignment: Alignment::Center,
                    include_page_numbers: true,
                }),
                styles: self.create_technical_styles(),
                metadata_overrides: Some(MetadataOverrides {
                    default_font: Some("Consolas".to_string()),
                    author: Some("Engineering Team".to_string()),
                    company: Some("ACME Corporation".to_string()),
                    department: Some("Engineering".to_string()),
                }),
                transformations: vec![],
                legacy_settings: None,
                enterprise_features: Some(EnterpriseFeatures {
                    digital_signature: false,
                    watermark: None,
                    classification: Some(DocumentClassification {
                        level: ClassificationLevel::Internal,
                        marking: "INTERNAL USE ONLY".to_string(),
                        handling_instructions: vec![
                            "For internal development use only".to_string(),
                        ],
                    }),
                    retention_policy: Some(RetentionPolicy {
                        retention_period_years: 5,
                        auto_delete: false,
                        archive_location: Some("project_archive".to_string()),
                    }),
                    access_control: Some(AccessControl {
                        required_roles: vec!["engineer".to_string(), "architect".to_string()],
                        department_restrictions: vec!["Engineering".to_string(), "Product".to_string()],
                        geographic_restrictions: vec![],
                    }),
                    audit_trail: true,
                }),
                conditional_content: vec![],
                localization: None,
                approval_workflow: Some(ApprovalWorkflow {
                    required_approvers: vec!["tech_lead".to_string(), "architect".to_string()],
                    approval_order: ApprovalOrder::Parallel,
                    auto_route: true,
                    deadline_days: Some(3),
                }),
            },
        );
    }

    /// Create memo styles
    fn create_memo_styles(&self) -> HashMap<String, StyleDefinition> {
        let mut styles = HashMap::new();
        
        styles.insert(
            "memo-heading".to_string(),
            StyleDefinition {
                name: "Memo Heading".to_string(),
                font: Some(FontSettings {
                    family: "Arial".to_string(),
                    size: 14,
                    bold: true,
                    italic: false,
                    underline: false,
                    color: Some("#000080".to_string()),
                }),
                paragraph: Some(ParagraphSettings {
                    alignment: Alignment::Center,
                    line_spacing: 1.5,
                    space_before: 12,
                    space_after: 6,
                    first_line_indent: 0,
                }),
                list: None,
            },
        );
        
        styles
    }

    /// Create report styles
    fn create_report_styles(&self) -> HashMap<String, StyleDefinition> {
        let mut styles = HashMap::new();
        
        styles.insert(
            "report-title".to_string(),
            StyleDefinition {
                name: "Report Title".to_string(),
                font: Some(FontSettings {
                    family: "Times New Roman".to_string(),
                    size: 18,
                    bold: true,
                    italic: false,
                    underline: false,
                    color: None,
                }),
                paragraph: Some(ParagraphSettings {
                    alignment: Alignment::Center,
                    line_spacing: 2.0,
                    space_before: 24,
                    space_after: 12,
                    first_line_indent: 0,
                }),
                list: None,
            },
        );
        
        styles
    }

    /// Apply template to document
    pub fn apply_template(
        &self,
        document: &mut RtfDocument,
        template_name: &str,
    ) -> ConversionResult<()> {
        let template = self.templates.get(template_name)
            .ok_or_else(|| ConversionError::GenerationError(
                format!("Template '{}' not found", template_name)
            ))?;

        // Apply metadata overrides
        if let Some(ref overrides) = template.metadata_overrides {
            self.apply_metadata_overrides(&mut document.metadata, overrides);
        }

        // Apply content transformations
        for transformation in &template.transformations {
            self.apply_transformation(&mut document.content, transformation)?;
        }

        // Add header/footer if configured
        if template.header.is_some() || template.footer.is_some() {
            self.add_header_footer(document, template)?;
        }

        Ok(())
    }

    /// Apply metadata overrides
    fn apply_metadata_overrides(
        &self,
        metadata: &mut DocumentMetadata,
        overrides: &MetadataOverrides,
    ) {
        if let Some(ref font) = overrides.default_font {
            metadata.default_font = Some(font.clone());
            
            // Ensure font is in font table
            if !metadata.fonts.iter().any(|f| &f.name == font) {
                metadata.fonts.push(FontInfo {
                    id: metadata.fonts.len() as i32,
                    name: font.clone(),
                    family: FontFamily::Swiss,
                });
            }
        }

        if let Some(ref author) = overrides.author {
            metadata.author = Some(author.clone());
        }
    }

    /// Apply content transformation
    fn apply_transformation(
        &self,
        content: &mut Vec<RtfNode>,
        transformation: &ContentTransformation,
    ) -> ConversionResult<()> {
        match transformation.transform_type {
            TransformationType::ApplyStyle => {
                self.apply_style_transformation(content, transformation)?;
            }
            TransformationType::AddWrapper => {
                self.apply_wrapper_transformation(content, transformation)?;
            }
            TransformationType::ReplacePattern => {
                self.apply_replace_transformation(content, transformation)?;
            }
            TransformationType::InsertElement => {
                self.apply_insert_transformation(content, transformation)?;
            }
            TransformationType::Restructure => {
                self.apply_restructure_transformation(content, transformation)?;
            }
        }
        
        Ok(())
    }

    /// Apply style transformation
    fn apply_style_transformation(
        &self,
        content: &mut Vec<RtfNode>,
        transformation: &ContentTransformation,
    ) -> ConversionResult<()> {
        let _style_name = transformation.parameters.get("style")
            .ok_or_else(|| ConversionError::GenerationError(
                "Style transformation requires 'style' parameter".to_string()
            ))?;

        // Apply to matching nodes
        self.transform_nodes_recursive(content, &transformation.target, &mut |node| {
            // In a real implementation, we would apply the style here
            // For now, we'll just mark it as transformed
            match node {
                RtfNode::Heading { .. } => {
                    // Apply heading style
                }
                RtfNode::Paragraph(_) => {
                    // Apply paragraph style
                }
                _ => {}
            }
        });

        Ok(())
    }

    /// Apply wrapper transformation
    fn apply_wrapper_transformation(
        &self,
        content: &mut Vec<RtfNode>,
        transformation: &ContentTransformation,
    ) -> ConversionResult<()> {
        let prefix = transformation.parameters.get("prefix").cloned().unwrap_or_default();
        let suffix = transformation.parameters.get("suffix").cloned().unwrap_or_default();

        match transformation.target {
            TransformTarget::FirstParagraph => {
                if let Some(first_para) = content.iter_mut().find(|n| matches!(n, RtfNode::Paragraph(_))) {
                    if let RtfNode::Paragraph(children) = first_para {
                        if !prefix.is_empty() {
                            children.insert(0, RtfNode::Text(prefix));
                        }
                        if !suffix.is_empty() {
                            children.push(RtfNode::Text(suffix));
                        }
                    }
                }
            }
            _ => {
                // Handle other targets
            }
        }

        Ok(())
    }

    /// Apply replace transformation
    fn apply_replace_transformation(
        &self,
        content: &mut Vec<RtfNode>,
        transformation: &ContentTransformation,
    ) -> ConversionResult<()> {
        let pattern = transformation.parameters.get("pattern")
            .ok_or_else(|| ConversionError::GenerationError(
                "Replace transformation requires 'pattern' parameter".to_string()
            ))?;
        let replacement = transformation.parameters.get("replacement").cloned().unwrap_or_default();

        self.replace_text_recursive(content, pattern, &replacement);
        
        Ok(())
    }

    /// Apply insert transformation
    fn apply_insert_transformation(
        &self,
        content: &mut Vec<RtfNode>,
        transformation: &ContentTransformation,
    ) -> ConversionResult<()> {
        let element_type = transformation.parameters.get("element")
            .ok_or_else(|| ConversionError::GenerationError(
                "Insert transformation requires 'element' parameter".to_string()
            ))?;
        let position = transformation.parameters.get("position").cloned().unwrap_or_else(|| "start".to_string());

        let new_element = match element_type.as_str() {
            "page_break" => RtfNode::PageBreak,
            "line_break" => RtfNode::LineBreak,
            _ => RtfNode::Text(format!("[{}]", element_type)),
        };

        match position.as_str() {
            "start" => content.insert(0, new_element),
            "end" => content.push(new_element),
            _ => {} // Handle other positions
        }

        Ok(())
    }

    /// Apply restructure transformation
    fn apply_restructure_transformation(
        &self,
        _content: &mut Vec<RtfNode>,
        _transformation: &ContentTransformation,
    ) -> ConversionResult<()> {
        // Complex restructuring logic would go here
        Ok(())
    }

    /// Transform nodes recursively
    fn transform_nodes_recursive<F>(
        &self,
        nodes: &mut Vec<RtfNode>,
        target: &TransformTarget,
        transform_fn: &mut F,
    ) where
        F: FnMut(&mut RtfNode),
    {
        for node in nodes.iter_mut() {
            let should_transform = match (target, &*node) {
                (TransformTarget::AllHeadings, RtfNode::Heading { .. }) => true,
                (TransformTarget::HeadingLevel(level), RtfNode::Heading { level: h_level, .. }) => h_level == level,
                (TransformTarget::AllParagraphs, RtfNode::Paragraph(_)) => true,
                (TransformTarget::AllLists, RtfNode::ListItem { .. }) => true,
                (TransformTarget::Tables, RtfNode::Table { .. }) => true,
                _ => false,
            };

            if should_transform {
                transform_fn(node);
            }

            // Recurse into children
            match node {
                RtfNode::Paragraph(ref mut children) |
                RtfNode::Bold(ref mut children) |
                RtfNode::Italic(ref mut children) |
                RtfNode::Underline(ref mut children) |
                RtfNode::Heading { content: ref mut children, .. } |
                RtfNode::ListItem { content: ref mut children, .. } => {
                    self.transform_nodes_recursive(children, target, transform_fn);
                }
                _ => {}
            }
        }
    }

    /// Replace text recursively
    fn replace_text_recursive(&self, nodes: &mut Vec<RtfNode>, pattern: &str, replacement: &str) {
        for node in nodes.iter_mut() {
            match node {
                RtfNode::Text(text) => {
                    *text = text.replace(pattern, replacement);
                }
                RtfNode::Paragraph(children) |
                RtfNode::Bold(children) |
                RtfNode::Italic(children) |
                RtfNode::Underline(children) |
                RtfNode::Heading { content: children, .. } |
                RtfNode::ListItem { content: children, .. } => {
                    self.replace_text_recursive(children, pattern, replacement);
                }
                RtfNode::Table { rows } => {
                    for row in rows {
                        for cell in &mut row.cells {
                            self.replace_text_recursive(&mut cell.content, pattern, replacement);
                        }
                    }
                }
                _ => {}
            }
        }
    }

    /// Add header and footer to document
    fn add_header_footer(
        &self,
        document: &mut RtfDocument,
        template: &DocumentTemplate,
    ) -> ConversionResult<()> {
        // In a real implementation, this would add proper RTF header/footer
        // For now, we'll add them as content at the beginning and end
        
        if let Some(ref header) = template.header {
            let header_text = self.process_template_variables(&header.content, &header.variables);
            document.content.insert(0, RtfNode::Paragraph(vec![
                RtfNode::Text(header_text),
                RtfNode::LineBreak,
            ]));
        }

        if let Some(ref footer) = template.footer {
            let footer_text = self.process_template_variables(&footer.content, &footer.variables);
            document.content.push(RtfNode::Paragraph(vec![
                RtfNode::LineBreak,
                RtfNode::Text(footer_text),
            ]));
        }

        Ok(())
    }

    /// Process template variables
    fn process_template_variables(&self, template: &str, variables: &HashMap<String, String>) -> String {
        let mut result = template.to_string();
        
        for (key, value) in variables {
            result = result.replace(&format!("{{{{{}}}}}", key), value);
        }
        
        // Handle built-in variables
        result = result.replace("{{date}}", &chrono::Local::now().format("%Y-%m-%d").to_string());
        result = result.replace("{{time}}", &chrono::Local::now().format("%H:%M:%S").to_string());
        
        result
    }

    /// Load custom template from file
    pub fn load_template_from_file(&mut self, path: &Path) -> ConversionResult<()> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| ConversionError::IoError(e.to_string()))?;
        
        let template: DocumentTemplate = serde_json::from_str(&content)
            .map_err(|e| ConversionError::ParseError(format!("Invalid template format: {}", e)))?;
        
        self.templates.insert(template.name.clone(), template);
        
        Ok(())
    }

    /// List available templates
    pub fn list_templates(&self) -> Vec<&str> {
        self.templates.keys().map(|k| k.as_str()).collect()
    }

    /// Get template by name
    pub fn get_template(&self, name: &str) -> Option<&DocumentTemplate> {
        self.templates.get(name)
    }

    /// Create financial report styles
    fn create_financial_styles(&self) -> HashMap<String, StyleDefinition> {
        let mut styles = HashMap::new();

        styles.insert("title".to_string(), StyleDefinition {
            font_family: Some("Arial".to_string()),
            font_size: Some(18),
            bold: Some(true),
            italic: Some(false),
            underline: Some(false),
            color: Some("#000080".to_string()),
            alignment: Some(Alignment::Center),
            line_spacing: Some(1.5),
            margin_top: Some(0),
            margin_bottom: Some(12),
        });

        styles.insert("section_header".to_string(), StyleDefinition {
            font_family: Some("Arial".to_string()),
            font_size: Some(14),
            bold: Some(true),
            italic: Some(false),
            underline: Some(true),
            color: Some("#000080".to_string()),
            alignment: Some(Alignment::Left),
            line_spacing: Some(1.2),
            margin_top: Some(12),
            margin_bottom: Some(6),
        });

        styles.insert("financial_data".to_string(), StyleDefinition {
            font_family: Some("Courier New".to_string()),
            font_size: Some(10),
            bold: Some(false),
            italic: Some(false),
            underline: Some(false),
            color: Some("#000000".to_string()),
            alignment: Some(Alignment::Right),
            line_spacing: Some(1.0),
            margin_top: Some(0),
            margin_bottom: Some(0),
        });

        styles
    }

    /// Create technical specification styles
    fn create_technical_styles(&self) -> HashMap<String, StyleDefinition> {
        let mut styles = HashMap::new();

        styles.insert("spec_title".to_string(), StyleDefinition {
            font_family: Some("Arial".to_string()),
            font_size: Some(16),
            bold: Some(true),
            italic: Some(false),
            underline: Some(false),
            color: Some("#000000".to_string()),
            alignment: Some(Alignment::Left),
            line_spacing: Some(1.3),
            margin_top: Some(0),
            margin_bottom: Some(12),
        });

        styles.insert("code_block".to_string(), StyleDefinition {
            font_family: Some("Consolas".to_string()),
            font_size: Some(9),
            bold: Some(false),
            italic: Some(false),
            underline: Some(false),
            color: Some("#000080".to_string()),
            alignment: Some(Alignment::Left),
            line_spacing: Some(1.0),
            margin_top: Some(6),
            margin_bottom: Some(6),
        });

        styles.insert("requirement".to_string(), StyleDefinition {
            font_family: Some("Arial".to_string()),
            font_size: Some(11),
            bold: Some(false),
            italic: Some(false),
            underline: Some(false),
            color: Some("#000000".to_string()),
            alignment: Some(Alignment::Left),
            line_spacing: Some(1.2),
            margin_top: Some(3),
            margin_bottom: Some(3),
        });

        styles
    }
}